import { useCommonStore } from '@/stores'
import {
  BusbarLoadService,
  type BusbarLoadDataDetailResponse,
} from '@/utils/api/services/busbarLoad'
import { formatTime } from '@/utils/tools'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

export const useCityData = () => {
  const route = useRoute()
  const commonStore = useCommonStore()

  const cityData = ref<BusbarLoadDataDetailResponse>({
    busLfDataList: [],
    busLfStatisticList: [],
  })

  const fetchBusbarLoadDataByRegion = async (timeRange: [number, number]) => {
    commonStore.loading = true
    try {
      const response = await BusbarLoadService.getBusbarLoadDataByRegion({
        day: formatTime(timeRange[0], 'YYYY-MM-DD'),
        regionCode: route.query.regionCode as string,
      })
      cityData.value = response
    } catch (error) {
      console.error('获取全省母线负荷数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  const fetchBusbarLoadDataByRegionMultiDay = async (timeRange: [number, number]) => {
    commonStore.loading = true
    try {
      const response = await BusbarLoadService.getBusbarLoadDataByRegionMultiDay({
        startDay: formatTime(timeRange[0], 'YYYY-MM-DD'),
        endDay: formatTime(timeRange[1], 'YYYY-MM-DD'),
        regionCode: route.query.regionCode as string,
      })
      cityData.value = response
    } catch (error) {
      console.error('获取全省母线负荷数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  return {
    cityData,
    fetchBusbarLoadDataByRegion,
    fetchBusbarLoadDataByRegionMultiDay,
  }
}
