<template>
  <div class="flex flex-col h-full">
    <!-- 顶部搜索表单 -->
    <SearchForm :loading="loading" @search="handleSearch">
      <template #form-fields>
        <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
          <n-date-picker
            v-model:value="cpsFrequencyStore.timeRange"
            class="w-full"
            type="datetimerange"
            size="large"
            placeholder="请选择时间范围"
            :is-date-disabled="disablePreviousDate"
          >
          </n-date-picker>
        </InputGroup>
      </template>

      <template #action-buttons>
        <ExportButton
          filename="CPS及频率数据"
          export-type="excel-structured"
          @export-start="handleExport"
          :structured-data="dataByExport"
        />
      </template>
    </SearchForm>

    <CpsChart></CpsChart>

    <!-- 底部数据展示区域 -->
    <DataTable
      class="p-5"
      :columns="tableColumns"
      :data="tableData"
      :loading="loading"
      height="500px"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NDatePicker } from 'naive-ui'
import DataTable from '@/components/shared/tables/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import CpsChart from '../common/CpsChart.vue'

import { disablePreviousDate } from '@/utils/tools/'
import { useCpsFrequencyStore } from '@/stores'
import { useCpsData, useCpsExport } from '@/hooks/'
const cpsFrequencyStore = useCpsFrequencyStore()

const { rtKeyData, loading } = useCpsData()
const { dataByExport, handleExport, getExportData } = useCpsExport()

// 默认表格列配置
const tableColumns = [
  {
    key: 'cps1',
    title: 'CPS1（全天）',
    align: 'center' as const,
  },
  {
    key: 'cps2',
    title: 'CPS2（全天）',
    align: 'center' as const,
  },
  {
    key: 'allDayFee',
    title: '全天费用',
    align: 'center' as const,
  },
  {
    key: 'rate',
    title: '频率',
    align: 'center' as const,
  },
]

// 默认表格数据
const tableData = computed(() => {
  if (!rtKeyData.value) return []

  return [
    {
      cps1: rtKeyData.value[0],
      cps2: rtKeyData.value[1],
      allDayFee: rtKeyData.value[2],
      rate: rtKeyData.value[3],
    },
  ]
})

// 搜索处理函数
const handleSearch = () => {
  cpsFrequencyStore.isInfoVisible = true
}
</script>

<style scoped></style>
