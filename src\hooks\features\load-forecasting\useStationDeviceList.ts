import { useBusbarLoadStore, useSiderbarStore } from '@/stores'
import {
  BusbarLoadService,
  type BusbarLoadExistDeviceListResponse,
} from '@/utils/api/services/busbarLoad'
import { formatTime, getTodayTimeRange } from '@/utils/tools'
import type { TreeSelectOption } from 'naive-ui'
import { onMounted } from 'vue'

/**
 * 查询母线负荷下的相关可查询设备列表
 * @returns
 */
export const useStationDeviceList = () => {
  const busbarLoadStore = useBusbarLoadStore()
  const siderbarStore = useSiderbarStore()

  const fetchStationDeviceList = async () => {
    try {
      const response = await BusbarLoadService.getBusbarLoadExistDeviceList({
        startTime: formatTime(getTodayTimeRange()[0], 'YYYY-MM-DD'),
        endTime: formatTime(getTodayTimeRange()[1], 'YYYY-MM-DD'),
      })
      formatData(response)

      siderbarStore.setCityOptions(response)
    } catch (error) {
      console.error('获取设备列表失败:', error)
    }
  }

  const formatData = (data: BusbarLoadExistDeviceListResponse[]) => {
    const list: TreeSelectOption[] = []
    data.forEach((item) => {
      const treeSelectOption = {
        ...item,
        label: item.name,
        key: item.id,
        stationId: item.id,
        children:
          item.deviceList.map((device) => ({
            ...device,
            label: device.name,
            key: device.id,
            deviceId: device.id,
          })) || [],
      }
      list.push(treeSelectOption)
    })
    busbarLoadStore.stationDeviceList = list
  }

  onMounted(() => {
    fetchStationDeviceList()
  })
}
