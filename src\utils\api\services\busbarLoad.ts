import { API_ENDPOINTS } from '../config'
import { api } from '../instance'

// 查获取全省母线负荷统计信息明细
export interface BusbarLoadDetailQueryParams {
  startDay?: string
  endDay?: string
}

/**
 * 母线负荷数据接口
 * time	时间
 * usPrediction	超短期预测值
 * rtValue	实测值
 * usDiff	差值
 */
export interface BusbarLoadDataItem {
  time: string
  usPrediction: string
  rtValue: string
  usDiff: string
}

/**
 * 母线负荷详情数据接口
 * area 地市名称
 * sPrediction	短期预测值
 * usPrediction	超短期预测值
 * rtValue	实时负荷
 * usDiff	超短期负荷偏差
 * usDiffRate	超短期负荷偏差率
 * dailyAvgUsDiff	超短期日均负荷偏差
 * dailyAvgUsDiffRate	超短期日均负荷偏差率
 * mae	MAE
 * mape	MAPE
 * rmse	RMSE
 */
export interface BusbarLoadDataDetailItem {
  area: string
  sPrediction: string
  usPrediction: string
  rtValue: string
  usDiff: string
  usDiffRate: string
  dailyAvgUsDiff: string
  dailyAvgUsDiffRate: string
  mae: string
  mape: string
  rmse: string
}

/**
 * 母线负荷详情数据接口
 * time	时间
 * usPrediction	超短期预测值
 * sPrediction	短期预测值
 * rtValue	实测值
 * usDiff	超短期偏差
 * usDiffRate	超短期偏差率
 */
export interface BusLfDataItem {
  time: string
  usPrediction: string
  sPrediction: string
  rtValue: string
  usDiff: string
  usDiffRate: string
}

// 母线负荷统计数据接口
export interface BusLfStatisticItem {
  date: string
  name: string
  dailyAvgUsDiff: string
  dailyAvgUsDiffRate: string
  mae: string
  rmse: string
  mape: string
  busLfDataList: BusLfDataItem[]
}

/**
 * 母线负荷详情响应数据接口
 * date	日期
 * name	名称
 * dailyAvgUsDiff	日平均偏差
 * dailyAvgUsDiffRate	日平均偏差率
 * busLfDataList	详细列表
 * mae	MAE
 * rmse	RMSE
 * mape	MAPE
 */
export interface BusbarLoadDataDetailResponse {
  busLfDataList: BusLfDataItem[]
  busLfStatisticList: BusLfStatisticItem[]
}

// 查询母线负荷下的相关可查询设备列表/获取地市母线负荷多日统计信息 接口参数
export interface BusbarLoadExistDeviceListParams {
  regionCode?: string
  startTime: string
  endTime: string
}

// 查询母线负荷下的相关可查询设备列表 返回值
export interface BusbarLoadExistDeviceListResponse {
  id: string
  name: string
  regionCode: string
  deviceList: {
    id: string
    name: string
    stationId: string
    stationName: string
    regionCode: string
  }[]
}

// 地市母线负荷单日统计信息 接口参数
export interface BusbarLoadDataByRegionParams {
  day: string
  regionCode: string
}

// 获取地市母线负荷多日统计信息
export interface BusbarLoadDataByRegionMultiDayParams {
  startDay: string
  endDay: string
  regionCode: string
}

export interface BusLfStatisticItemWithStation extends BusLfStatisticItem {
  stationId: string
}

// 地市母线负荷单日统计信息 返回值
export interface BusbarLoadDataByRegionResponse {
  busLfDataList: BusLfDataItem[]
  busLfStatisticList: BusLfStatisticItemWithStation[]
}

// 获取场站母线负荷统计信息
export interface BusbarLoadDataByStationParams {
  startDay: string
  endDay: string
  stationId: string
}

// 获取设备母线负荷统计信息
export interface BusbarLoadDataByDeviceParams {
  startDay: string
  endDay: string
  deviceId: string
}

export class BusbarLoadService {
  /**
   * 获取全省母线负荷数据
   * @param params 查询参数
   * @returns 母线负荷数据
   */
  static async getBusbarLoadData(params: { day: string }) {
    return api.get<BusbarLoadDataItem[]>(API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA, params)
  }

  /**
   * 获取全省母线负荷单个时间详情
   * @param params 查询参数
   * @returns 母线负荷单个时间详情
   */
  static async getBusbarLoadDataByTime(params: { time: string }) {
    return api.get<BusbarLoadDataDetailItem[]>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_BY_TIME,
      params,
    )
  }
  /**
   * 获取全省母线负荷统计信息明细
   * @param params 查询参数
   * @returns 母线负荷统计信息明细
   */
  static async getBusbarLoadDataDetail(params: BusbarLoadDetailQueryParams) {
    return api.get<BusbarLoadDataDetailResponse>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_DETAIL,
      params,
    )
  }

  /**
   * 查询母线负荷下的相关可查询设备列表
   * @param params
   * @returns
   */
  static async getBusbarLoadExistDeviceList(params: BusbarLoadExistDeviceListParams) {
    return api.get<BusbarLoadExistDeviceListResponse[]>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_EXIST_DEVICE_LIST,
      params,
    )
  }

  /**
   *  获取地市母线负荷单日统计信息
   * @param params
   * @returns
   */
  static async getBusbarLoadDataByRegion(params: BusbarLoadDataByRegionParams) {
    return api.get<BusbarLoadDataDetailResponse>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_BY_REGION,
      params,
    )
  }

  /**
   * 获取地市母线负荷多日统计信息
   * @param params
   * @returns
   */
  static async getBusbarLoadDataByRegionMultiDay(params: BusbarLoadDataByRegionMultiDayParams) {
    return api.get<BusbarLoadDataByRegionResponse>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_BY_REGION_MULTI_DAY,
      params,
    )
  }

  /**
   * 获取场站母线负荷统计信息
   * @param params
   * @returns
   */
  static async getBusbarLoadDataByStation(params: BusbarLoadDataByStationParams) {
    return api.get<BusbarLoadDataDetailResponse>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_BY_STATION,
      params,
    )
  }

  /**
   * 获取设备母线负荷统计信息
   * @param params
   * @returns
   */
  static async getBusbarLoadDataByDevice(params: BusbarLoadDataByDeviceParams) {
    return api.get<BusbarLoadDataDetailResponse>(
      API_ENDPOINTS.LOAD_FORECASTING.BUSBAR_LOAD_DATA_BY_DEVICE,
      params,
    )
  }
}
