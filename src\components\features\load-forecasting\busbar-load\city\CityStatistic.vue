<template>
  <div class="flex flex-col h-full">
    <!-- 顶部搜索表单 -->
    <SearchForm @search="handleSearch">
      <template #form-fields>
        <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
          <n-date-picker
            v-model:value="timeRange"
            class="w-full"
            size="large"
            type="daterange"
            placeholder="请选择时间"
            :is-date-disabled="disablePreviousDate"
          >
          </n-date-picker>
        </InputGroup>
        <InputGroup v-if="isCity" size="large" label="名称" class="mr-2.5 max-w-317px">
          <n-tree-select
            v-model:value="stationDevice"
            :options="stationDeviceOptions"
            size="large"
            filterable
            placeholder="请选择名称"
            clearable
            @update-value="handleStationDeviceChange"
          >
          </n-tree-select>
        </InputGroup>
      </template>

      <template #action-buttons>
        <!-- <ExportButton
          :data="tableData"
          :columns="tableColumns.slice(0, 4)"
          filename="全省母线负荷数据"
        /> -->
      </template>
    </SearchForm>
    <LineChart ref="chartRef" :data="chartData" yAxisName="万千瓦" height="450px" class="mt-2" />

    <n-scrollbar style="height: calc(100vh - 580px)">
      <div v-for="item in data.busLfStatisticList" class="flex flex-col pl-4 mb-5">
        <div class="flex">
          <CityDetailCard :isMultiDay="isMultiDay" class="w-50" :data="item" />
          <div class="flex flex-col px-5">
            <!-- 横向表格数据 -->
            <VerticalTable
              class="flex-1"
              width="calc(100vw - 755px)"
              :columns="tableColumns"
              :data="item.busLfDataList"
            ></VerticalTable>
          </div>
        </div>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue'
import { NDatePicker, NTreeSelect, NScrollbar, type TreeSelectOption } from 'naive-ui'

import {
  disablePreviousDate,
  getTodayTimeRange,
  isTimeRangeCrossDay,
  isTimeRangeExceed,
} from '@/utils/tools/'
import { useRoute, useRouter } from 'vue-router'
import { useCityData, useStationDeviceData } from '@/hooks'
import type { SeriesData, TableColumn } from '@/types'

import LineChart from '@/components/shared/charts/LineChart.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import CityDetailCard from './CityLoadDetailCard.vue'
import VerticalTable from '@/components/shared/tables/VerticalTable.vue'
import { useBusbarLoadStore, useSiderbarStore } from '@/stores'

const busbarLoadStore = useBusbarLoadStore()
const router = useRouter()
const route = useRoute()

const stationDevice = ref('')

const timestampRange = getTodayTimeRange()

const timeRange = ref<[number, number]>(timestampRange)
const searchTimeRange = ref<[number, number]>(timestampRange)

const tableColumns: TableColumn[] = [
  {
    key: 'time',
    title: '时间',
  },
  {
    key: 'sPrediction',
    title: '短期预测',
  },
  {
    key: 'usPrediction',
    title: '超短期预测',
  },
  {
    key: 'rtValue',
    title: '实时负荷',
  },
  {
    key: 'usDiff',
    title: '负荷偏差',
  },
  {
    key: 'usDiffRate',
    title: '负荷偏差率',
  },
]

const chartData = computed<SeriesData[]>(() => {
  if (!data.value.busLfDataList.length) return []

  return [
    {
      name: '负荷预测',
      color: 'rgba(206, 66, 174, 1)',
      isShowAreaStyle: true,
      data: data.value.busLfDataList.map((item) => ({
        name: item.time,
        value: item.usPrediction,
      })),
    },
    {
      name: '负荷实测',
      color: 'rgba(12, 163, 159, 1)',
      isShowAreaStyle: true,
      data: data.value.busLfDataList.map((item) => ({
        name: item.time,
        value: item.rtValue,
      })),
    },
    {
      name: '负荷偏差',
      color: '#004EE4',
      yAxisIndex: 2,
      data: data.value.busLfDataList.map((item) => ({
        name: item.time,
        value: item.usDiff,
      })),
    },
  ]
})

const { cityData, fetchBusbarLoadDataByRegion, fetchBusbarLoadDataByRegionMultiDay } = useCityData()

const { stationDeviceData, fetchBusbarLoadDataByStation, fetchBusbarLoadDataByDevice } =
  useStationDeviceData()

const data = computed(() => {
  if (route.query.stationId || route.query.deviceId) {
    return stationDeviceData.value
  } else {
    return cityData.value
  }
})

// 搜索处理函数
const handleSearch = () => {
  // 判断时间范围是否超过31天
  if (isTimeRangeExceed(timeRange.value)) {
    window.$message.warning('时间范围不能超过31天')
    return
  }
  searchTimeRange.value = timeRange.value
}

const siderbarStore = useSiderbarStore()

const handleStationDeviceChange = (value: string, item: TreeSelectOption) => {
  if (item.deviceId) {
    router.push({
      path: '/load-forecasting/busbar-load',
      query: {
        key: item.key as string,
        regionCode: item.regionCode as string,
        deviceId: item.key as string,
      },
    })
  } else if (item.stationId) {
    router.push({
      path: '/load-forecasting/busbar-load',
      query: {
        key: item.key as string,
        regionCode: item.regionCode as string,
        stationId: item.key as string,
      },
    })
  }
  siderbarStore.menuRef?.showOption(item.key)
}

const isMultiDay = computed(() => {
  return isTimeRangeCrossDay(searchTimeRange.value)
})

watchEffect(() => {
  const { stationId, deviceId, region } = route.query

  if (stationId) {
    fetchBusbarLoadDataByStation(searchTimeRange.value)
  } else if (deviceId) {
    fetchBusbarLoadDataByDevice(searchTimeRange.value)
  } else if (region) {
    if (isMultiDay.value) {
      fetchBusbarLoadDataByRegionMultiDay(searchTimeRange.value)
    } else {
      fetchBusbarLoadDataByRegion(searchTimeRange.value)
    }
  }
})

const isCity = computed(() => {
  return !!route.query.region
})

// 根据regionCode筛选busbarLoadStore中的stationDeviceList
const stationDeviceOptions = computed(() => {
  return busbarLoadStore.stationDeviceList.filter(
    (item) => item.regionCode === route.query.regionCode,
  )
})
</script>

<style></style>
