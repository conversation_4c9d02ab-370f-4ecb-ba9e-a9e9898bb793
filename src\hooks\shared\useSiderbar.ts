import { useSiderbarStore } from '@/stores'
import { type MenuOption } from 'naive-ui'

import { ref, watchEffect } from 'vue'
import { useRouter } from 'vue-router'

export const useSiderbar = () => {
  // 路由实例
  const router = useRouter()
  const siderbarStore = useSiderbarStore()

  const activeKey = ref('section-monitoring')

  watchEffect(() => {
    const { query, name } = router.currentRoute.value
    activeKey.value = query.key ? (query.key as string) : (name as string)
    siderbarStore.menuRef?.showOption(activeKey.value)
  })

  /**
   * 如果item.route有值，则将key作为route的参数 ?key=
   * @param key
   * @param item
   */
  const handleMenuSelect = (key: string, item: MenuOption) => {
    activeKey.value = key
    if (item.route) {
      const queryParams: Record<string, string> = {
        key,
      }
      if (item.region) {
        queryParams['region'] = item.region as string
      }
      if (item.regionCode) {
        queryParams['regionCode'] = item.regionCode as string
      }
      if (item.stationId) {
        queryParams['stationId'] = item.stationId as string
      }
      if (item.deviceId) {
        queryParams['deviceId'] = item.deviceId as string
      }
      router.push({
        path: item.route as string,
        query: queryParams,
      })
    } else {
      router.push(`/${key}`)
    }
  }

  return {
    activeKey,
    handleMenuSelect,
  }
}
