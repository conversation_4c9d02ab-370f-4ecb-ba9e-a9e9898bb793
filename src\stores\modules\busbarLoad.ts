import { defineStore } from 'pinia'
import { ref } from 'vue'

import LineChart from '@/components/shared/charts/LineChart.vue'
import { getTodayTimeRange } from '@/utils/tools'
import type { BusbarLoadDataItem } from '@/utils/api/services/busbarLoad'
import type { TreeSelectOption } from 'naive-ui'

export const useBusbarLoadStore = defineStore('busbarLoad', () => {
  const isProvinceInfoVisible = ref<boolean>(false)
  const isProvinceDetailVisible = ref<boolean>(false)

  const timestampRange = getTodayTimeRange()

  const timeRange = ref<[number, number]>([timestampRange[0], timestampRange[1]])

  const chartRef = ref<InstanceType<typeof LineChart> | null>(null)

  const selectedRow = ref<BusbarLoadDataItem | null>(null)

  const stationDeviceList = ref<TreeSelectOption[]>([])

  return {
    isProvinceInfoVisible,
    isProvinceDetailVisible,
    timeRange,
    chartRef,
    selectedRow,
    stationDeviceList,
  }
})
