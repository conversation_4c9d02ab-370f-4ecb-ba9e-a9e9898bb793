<template>
  <!-- 日期详情数据 -->
  <div class="flex flex-shrink-0 flex-col w-40.5 rounded" :class="cardBackgroundClass">
    <div class="text-xl mt-3 mb-2 ml-4" :class="headerTextClass">
      <div class="flex items-center mb-2" v-if="data.date">
        <n-icon size="18">
          <CalendarAltRegularIcon />
        </n-icon>
        <span class="ml-2"> {{ data.date }}</span>
      </div>
      <div class="flex items-center">
        <n-icon size="22" class="scale-90 -translate-x-0.5" :color="locationIconColor">
          <LocationIcon />
        </n-icon>
        <span class="ml-2"> {{ data.name }}</span>
      </div>
    </div>
    <div class="flex flex-1 flex-col pl-2 pb-4 justify-between">
      <div class="flex items-center -mt-2">
        <div class="flex items-center" :class="labelTextClass">
          <span class="mb-1 text-5xl mr-0.5">·</span>
          <span class="text-base">日均偏差</span>
        </div>
        <div class="text-xl ml-2" :class="valueTextClass">{{ data.dailyAvgUsDiff }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center" :class="labelTextClass">
          <span class="mb-1 text-5xl mr-0.5">·</span>
          <span class="text-base">日均偏差率</span>
        </div>
        <div class="text-xl ml-2" :class="valueTextClass">{{ data.dailyAvgUsDiffRate }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center" :class="labelTextClass">
          <span class="text-5xl mr-0.5">·</span>
          <span class="mt-1 text-base">MAE</span>
        </div>
        <div class="text-xl ml-5" :class="valueTextClass">{{ data.mae }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center" :class="labelTextClass">
          <span class="text-5xl mr-0.5">·</span>
          <span class="mt-1 text-base">RMSE</span>
        </div>
        <div class="text-xl ml-5" :class="valueTextClass">{{ data.rmse }}</div>
      </div>
      <div class="flex items-center -mt-5">
        <div class="flex items-center" :class="labelTextClass">
          <span class="text-5xl mr-0.5">·</span>
          <span class="mt-1 text-base">MAPE</span>
        </div>
        <div class="text-xl ml-5" :class="valueTextClass">{{ data.mape }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NIcon } from 'naive-ui'
import type { BusLfStatisticItem } from '@/utils/api/services/busbarLoad'
import { CalendarAltRegularIcon, LocationIcon } from '@/utils/constant/icons'

const props = withDefaults(
  defineProps<{
    data: BusLfStatisticItem
    isStationDevice?: boolean
  }>(),
  {
    data: () => ({
      date: '',
      name: '',
      dailyAvgUsDiff: '',
      dailyAvgUsDiffRate: '',
      mae: '',
      rmse: '',
      mape: '',
      busLfDataList: [],
    }),
    isStationDevice: false,
  },
)

// 根据 isStationDevice 动态计算样式类
const cardBackgroundClass = computed(() => {
  return props.isStationDevice ? 'bg-[#DCF3EB]' : 'bg-#FEF4E8'
})

const headerTextClass = computed(() => {
  return props.isStationDevice ? 'text-#178C64' : 'text-[#C76920]'
})

const labelTextClass = computed(() => {
  return props.isStationDevice ? 'text-#55A287' : 'text-[#CC8F60]'
})

const valueTextClass = computed(() => {
  return props.isStationDevice ? 'text-#13593F' : 'text-[#643F1A]'
})

const locationIconColor = computed(() => {
  return props.isStationDevice ? '#0e7a0d' : undefined
})
</script>

<style></style>
