<template>
  <SearchForm :loading="loading" showReset @search="handleSearch" @reset="handleReset">
    <template #form-fields>
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-300px">
        <n-date-picker
          v-model:value="time"
          class="w-full"
          size="large"
          placeholder="请选择时间"
          :is-date-disabled="disablePreviousDate"
        >
        </n-date-picker>
      </InputGroup>
    </template>

    <template #action-buttons>
      <n-button size="large" @click="handleExport" :loading="loading">
        <template #icon>
          <n-icon><ExportIcon /></n-icon>
        </template>
        导出日报
      </n-button>
    </template>
  </SearchForm>
  <div class="px-7.5">
    <div class="py-5">
      <div class="text-#5F6673 text-xl font-bold">新能源市场出清调峰情况</div>
      <span class="text-#A1A9B7 text-base">{{ description }} </span>
    </div>

    <div class="flex">
      <div class="w-[calc(100%-480px)] flex-shrink flex-grow">
        <!-- <div class="relative">
          <div class="absolute text-xl text-#5F6673 font-bold">新能源弃电情况统计</div> -->
        <LineChart
          ref="statisticChartRef"
          title="新能源弃电情况统计"
          :data="statisticChartData"
          height="300px"
          :custom-config="customConfig"
        />
        <!-- </div>
        <div class="relative">
          <div class="absolute text-xl text-#5F6673 font-bold">风力发电</div> -->
        <LineChart
          ref="windChartRef"
          title="风力发电"
          :data="windChartData"
          height="300px"
          :custom-config="customConfig"
        />
        <!-- </div>
        <div class="relative">
          <div class="absolute text-xl text-#5F6673 font-bold">光伏发电</div> -->
        <LineChart
          ref="solarChartRef"
          title="光伏发电"
          :data="solarChartData"
          height="300px"
          :custom-config="customConfig"
        />
        <!-- </div> -->
      </div>
      <div class="w-120 mt-1">
        <div class="text-#5F6673 text-xl font-bold mb-2.5">调峰时段分析</div>
        <DataTable height="820px" :columns="peakShavingColumns" :data="peakShavingData" />
      </div>
    </div>

    <div class="text-#5F6673 text-xl font-bold mb-2.5">风电、光伏厂站调峰明细</div>
    <div v-for="item in peakingTimeDetailList">
      <p class="text-#A1A9B7 text-xl mt-3 mb-2">{{ item.time }} 调峰时段:</p>
      <DataTable :columns="peakingTimeDetailColumns" :data="item.peakingDetailList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, useTemplateRef } from 'vue'

import { NButton, NIcon, NDatePicker } from 'naive-ui'
import { ExportIcon } from '@/utils/constant/icons'
import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/tables/DataTable.vue'
import { disablePreviousDate, formatTime } from '@/utils/tools'
import SearchForm from '@/components/shared/SearchForm.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import { useExportDaliyReport, useDaliyReport } from '@/hooks/'
import dayjs from 'dayjs'

const statisticChartRef = useTemplateRef('statisticChartRef')
const windChartRef = useTemplateRef('windChartRef')
const solarChartRef = useTemplateRef('solarChartRef')

const {
  customConfig,
  peakingTimeDetailColumns,
  peakingTimeDetailExportColumns,
  peakingTimeDetailList,
  peakShavingColumns,
  peakShavingData,
  windChartData,
  statisticChartData,
  solarChartData,
  description,
  fetchDailyReportData,
} = useDaliyReport()

const { exportWord } = useExportDaliyReport()

const loading = ref(false)
const time = ref<number>(dayjs().valueOf())

// 2025-07-09
const handleSearch = () => {
  fetchDailyReportData(formatTime(time.value, 'YYYY-MM-DD'))
}

const formatTableData = (columns: any[], data: any[]) => {
  const columnsRes = columns.map((item) => item.title)

  const dataRes = data.map((item) => columns.map((column) => item[column.key]))
  return [columnsRes, ...dataRes]
}
const handleExport = () => {
  const statisticChartImage = statisticChartRef.value?.getImg()
  const windChartImage = windChartRef.value?.getImg()
  const solarChartImage = solarChartRef.value?.getImg()
  const peakShavingTableData = formatTableData(
    peakShavingColumns.concat({ key: 'reason', title: '调峰原因分析', width: '30%' }),
    peakShavingData.value,
  )

  const peakingTimeDetailTableData = peakingTimeDetailList.value.map((item) => {
    return {
      time: item.time,
      data: formatTableData(peakingTimeDetailExportColumns, item.peakingDetailList),
    }
  })

  const chartTime = formatTime(time.value, 'M月DD日')

  exportWord({
    // 实时现货市场运行偏差分析日报2025年6月30日
    fileName: `实时现货市场运行偏差分析日报${formatTime(time.value, 'YYYY年MM月DD日')}`,
    statisticChartImage,
    windChartImage,
    solarChartImage,
    chartTime,
    peakShavingData: peakShavingTableData,
    peakingTimeDetailTableData,
    description: description.value + '参与调峰时段分析如下：',
  })
}

const handleReset = () => {
  time.value = 0
  handleSearch()
}

onMounted(() => {
  handleSearch()
})
</script>
