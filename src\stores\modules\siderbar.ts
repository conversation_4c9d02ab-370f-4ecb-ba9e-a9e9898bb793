import { shallowRef, computed, ref } from 'vue'
import { defineStore } from 'pinia'
import type { MenuOption, NMenu } from 'naive-ui'
import type { BusbarLoadExistDeviceListResponse } from '@/utils/api/services/busbarLoad'
import { cityRawOptions, menuRawOptions } from '@/utils/constant/options'

export const useSiderbarStore = defineStore('siderbar', () => {
  // 使用 shallowRef 提升大型嵌套对象的性能
  const cityData = shallowRef<MenuOption[]>([])
  const regionCodeMap = new Map<string, MenuOption>()
  const expandedKeys = ref<string[]>([])
  const menuRef = ref<InstanceType<typeof NMenu> | null>(null)

  // 深度克隆菜单选项的辅助函数，处理包含函数的对象
  const deepCloneMenuOptions = (options: MenuOption[]): MenuOption[] => {
    return options.map((option) => ({
      ...option,
      children: option.children ? deepCloneMenuOptions(option.children) : undefined,
    }))
  }

  // 计算属性用于菜单选项，避免不必要的更新
  const menuOptions = computed(() => {
    const updatedMenu = deepCloneMenuOptions(menuRawOptions)
    const loadForecast = updatedMenu.find((item) => item.key === 'load-forecasting')
    const busbarLoad = loadForecast?.children?.find((item) => item.key === 'busbar-load')

    if (busbarLoad && cityData.value.length > 0) {
      busbarLoad.children = cityData.value
    }

    return updatedMenu
  })

  const resetCityOptions = () => {
    // 使用浅克隆提升性能，label已在options中预渲染
    cityData.value = cityRawOptions.map((city) => ({ ...city }))
    regionCodeMap.clear()

    // 更高效地构建区域代码映射
    cityData.value.forEach((city) => {
      if (city.regionCode) {
        regionCodeMap.set(city.regionCode as string, city)
      }
    })
  }

  const setCityOptions = (data: BusbarLoadExistDeviceListResponse[]) => {
    if (!data?.length) return

    resetCityOptions()

    // 预分配数组并使用批量操作
    const stationsToAdd = new Map<string, MenuOption[]>()

    // 批量处理数据以避免阻塞主线程
    data.forEach((item) => {
      const deviceChildren = item.deviceList.map((device) => ({
        label: device.name,
        key: device.id,
        deviceId: device.id,
        regionCode: device.regionCode,
        route: '/load-forecasting/busbar-load',
      }))

      const station: MenuOption = {
        label: item.name,
        key: `${item.id}-${item.regionCode}`,
        regionCode: item.regionCode,
        children: [
          {
            label: item.name,
            key: item.id,
            stationId: item.id,
            regionCode: item.regionCode,
            route: '/load-forecasting/busbar-load',
          },
          ...deviceChildren,
        ],
      }

      // 按区域代码分组站点以便批量插入
      if (!stationsToAdd.has(item.regionCode)) {
        stationsToAdd.set(item.regionCode, [])
      }
      stationsToAdd.get(item.regionCode)!.push(station)
    })

    // 批量更新城市数据
    stationsToAdd.forEach((stations, regionCode) => {
      const city = regionCodeMap.get(regionCode)
      if (city) {
        city.children = [...(city.children ?? []), ...stations]
      }
    })

    // 触发响应式更新
    cityData.value = [...cityData.value]
  }

  return {
    menuRef,
    expandedKeys,
    menuOptions,
    setCityOptions,
    resetCityOptions,
  }
})
