import { useCpsFrequencyStore } from '@/stores'
import type { ExportBlockOptions } from '@/utils/export'
import { ref } from 'vue'

export const useCpsExport = () => {
  const cpsFrequencyStore = useCpsFrequencyStore()
  const dataByExport = ref<ExportBlockOptions>()

  const getExportData = () => {
    const data = cpsFrequencyStore.cpsData?.CPSStatisticList.map((item) => {
      const timeList = item.cps1List.map((item) => item.time)
      return [
        {
          title: '日期',
          value: item.date,
          style: {
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFACB9CA' },
            },
            font: {
              bold: true,
            },
          },
        },
        {
          title: '',
          value: [
            {
              title: 'CPS1',
              value: item.cps1,
            },
            {
              title: 'CPS2',
              value: item.cps2,
            },
            {
              title: '全日电费',
              value: item.allDayFee,
            },
            {
              title: '最大频率',
              value: item.maxRate,
            },
            {
              title: '最小频率',
              value: item.minRate,
            },
          ],
        },
        {
          title: '时间',
          value: timeList,
        },
        {
          title: 'cps1',
          value: item.cps1List.map((item) => item.value),
        },
        {
          title: 'cps2',
          value: item.cps2List.map((item) => item.value),
        },
        {
          title: '频率',
          value:
            timeList.map((time) => {
              const target = cpsFrequencyStore.cpsData?.JSRateList.find(
                (item) => item.time === time,
              )
              return target ? target.value : ''
            }) || [],
        },
      ]
    })

    return {
      list: data,
      image: cpsFrequencyStore.chartRef?.getImg().base64 || '',
    }
  }

  const handleExport = () => {
    dataByExport.value = getExportData()
    console.log('🚀 ~ handleExport ~ dataByExport.value:', dataByExport.value)
  }

  return {
    getExportData,
    dataByExport,
    handleExport,
  }
}
