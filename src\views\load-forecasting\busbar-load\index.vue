<template>
  <component :is="isProvince ? Province : City"></component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Province from '@/components/features/load-forecasting/busbar-load/province/index.vue'
import City from '@/components/features/load-forecasting/busbar-load/city/CityStatistic.vue'

const route = useRoute()

const isProvince = computed(() => route.query.key === 'province' || !route.query.key)
</script>

<style scoped></style>
