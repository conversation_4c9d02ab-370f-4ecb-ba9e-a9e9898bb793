import { useSiderbarStore } from '@/stores'
import type { TreeSelectOption } from 'naive-ui'
import { useRouter } from 'vue-router'

export const useTreeSelect = () => {
  const router = useRouter()
  const siderbarStore = useSiderbarStore()

  const handleStationDeviceChange = (value: string, item: TreeSelectOption) => {
    if (item.deviceId) {
      router.push({
        path: '/load-forecasting/busbar-load',
        query: {
          key: item.key as string,
          regionCode: item.regionCode as string,
          deviceId: item.key as string,
          name: item.name as string,
        },
      })
    } else if (item.stationId) {
      router.push({
        path: '/load-forecasting/busbar-load',
        query: {
          key: item.key as string,
          regionCode: item.regionCode as string,
          stationId: item.key as string,
          name: item.name as string,
        },
      })
    }
    siderbarStore.menuRef?.showOption(item.key)
  }

  return {
    handleStationDeviceChange,
  }
}
